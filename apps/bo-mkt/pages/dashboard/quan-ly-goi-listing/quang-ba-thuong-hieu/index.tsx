//#region Imports
import {
  MPBrandPromotion,
  openNotification,
  showError
} from '@medpro-libs/medpro-component-libs'
import { useEffect, useMemo, useState } from 'react'
import { useDispatch } from 'react-redux'
import { useClientSDK } from '../../../../hooks/useClientSDK'
import { useAppSelector } from '../../../../store/hooks'
import { hospitalActions } from '../../../../store/hospital/slice'
import { useDataFetching } from '../../../../hooks/useDataFetching'

//#region Mock Data

export default function BrandPromotionPage(props: any) {
  const { session, heightContent, environment } = props
  const { client } = useClientSDK(session)
  const dispatch = useDispatch()
  const loading = useAppSelector((state) => state?.total?.loading)
  const hospitalLisTab = useAppSelector((state) => state?.hospital?.list)

  //#region States
  const [fetchLoading, setFetchLoading] = useState<boolean>(false)
  const [filter, setFilter] = useState<any>({
    pageIndex: 0,
    pageSize: 10
  })
  const [selectedFilterTab, setSelectedFilterTab] = useState<any>('ALL')
  const [tabActiveKey, setTabActiveKey] = useState('partner')

  // Sử dụng custom hook để quản lý data fetching
  const {
    data,
    loading: dataLoading,
    refreshData,
    fetchAllDoctorTelemed
  } = useDataFetching(client, environment, selectedFilterTab)

  // Destructure data từ custom hook
  const {
    partner: dataPartner,
    doctorTelemed: doctorTelemedList,
    allDoctorTelemed: allDoctorTelemedList,
    csytFavourite: dataCSYTFavourite
  } = data

  // Combined loading state
  const isLoading =
    fetchLoading || loading?.status || Object.values(dataLoading).some(Boolean)

  // #region Short table
  const onDragSortTable = async (data: any) => {
    if (data.length <= 1) {
      return false
    }
    setFetchLoading(true)
    try {
      const ids = data && data.length ? data.map((item: any) => item._id) : []
      await client?.homepageModule.sortOrderHomePgaeModule(
        {
          ids,
          repo: environment.code,
          type: tabActiveKey
        },
        { appid: 'medpro' }
      )
      openNotification('success', { message: 'Thao tác thành công' })
      // Refresh data after successful sort
      await refreshData('all')
    } catch (error) {
      showError(error)
    } finally {
      setFetchLoading(false)
    }
  }

  //#region UseEffect
  useEffect(() => {
    dispatch(hospitalActions.getHospitalList({ ...session }))
  }, [session, dispatch])
  //#region Handlers
  const handleFilter = (newFilter: any) => {
    setFilter((prevFilter: any) => {
      return {
        ...prevFilter,
        ...newFilter
      }
    })
    // setFetchLoading(true)
  }
  // #region Handlers
  // Handle TTHT và đồng hành - Updated to use custom hook
  const methodsDoctorTelemed = useMemo(
    () => ({
      getListDoctorTelemed: async ({ tab }: { tab: string }) => {
        // This is now handled by the custom hook
        await refreshData('doctorTelemed')
      },

      getAllDoctorTelemed: async () => {
        await fetchAllDoctorTelemed()
      },

      onAdd: async (body: any) => {
        try {
          await client?.homepageModule.addDoctorTelemed({
            ...body,
            repo: environment.code
          })
          openNotification('success', { message: 'Thêm mới bác sĩ thành công' })
          await refreshData('doctorTelemed')
        } catch (error) {
          showError(error)
        }
      },

      onUpdate: async (body: any) => {
        try {
          await client?.homepageModule.updateDoctorTelemed({
            ...body,
            repo: environment.code
          })
          openNotification('success', { message: 'Cập nhật bác sĩ thành công' })
          await refreshData('doctorTelemed')
        } catch (error) {
          showError(error)
        }
      },

      onDelete: async (id: string) => {
        try {
          await client?.homepageModule.deleteDoctorTelemed(id)
          openNotification('success', { message: 'Xóa bác sĩ thành công' })
          await refreshData('doctorTelemed')
        } catch (error) {
          showError(error)
        }
      }
    }),
    [
      client?.homepageModule,
      environment.code,
      refreshData,
      fetchAllDoctorTelemed
    ]
  )
  const actionCompanionship = useMemo(
    () => ({
      create: async (values: any, cancelModal: any) => {
        setFetchLoading(true)
        try {
          await client?.homepageModule.createPackage(
            {
              ...values,
              repo: 'testing'
            },
            {
              appid: 'medpro'
            }
          )
          openNotification('success', { message: 'Tạo mới thành công' })
          onRefresh()
          cancelModal()
          await refreshData('partner')
        } catch (error) {
          showError(error)
        } finally {
          setFetchLoading(false)
        }
      },
      update: async (values: any, cancelModal: any) => {
        setFetchLoading(true)
        try {
          await client?.homepageModule.updatePackage(values._id, {
            ...values
          })
          openNotification('success', { message: 'Cập nhật thành công' })
          onRefresh()
          cancelModal()
          await refreshData('partner')
        } catch (error) {
          showError(error)
        } finally {
          setFetchLoading(false)
        }
      },
      delete: async (id: string) => {
        setFetchLoading(true)
        try {
          await client?.homepageModule.deletePackage(id)
          openNotification('success', { message: 'Xóa thành công' })
          onRefresh()
          await refreshData('partner')
        } catch (error) {
          showError(error)
        } finally {
          setFetchLoading(false)
        }
      }
    }),
    [client?.homepageModule, refreshData]
  )

  // Handle Cơ sở y tế yêu thích
  const actionCSYTFavourite = useMemo(
    () => ({
      create: async (values: any, cancelModal: any) => {
        setFetchLoading(true)
        try {
          await client?.homepageModule.createHospital(
            {
              ...values,
              repo: 'testing'
            },
            {
              appid: 'medpro'
            }
          )
          openNotification('success', { message: 'Tạo mới thành công' })
          onRefresh()
          cancelModal()
          await refreshData('csytFavourite')
        } catch (error) {
          showError(error)
        } finally {
          setFetchLoading(false)
        }
      },
      update: async (values: any, cancelModal: any) => {
        setFetchLoading(true)
        try {
          await client?.homepageModule.updateHospital(values._id, {
            ...values
          })
          openNotification('success', { message: 'Cập nhật thành công' })
          onRefresh()
          cancelModal()
          await refreshData('csytFavourite')
        } catch (error) {
          showError(error)
        } finally {
          setFetchLoading(false)
        }
      },
      delete: async (id: string) => {
        setFetchLoading(true)
        try {
          await client?.homepageModule.deleteHospital(id)
          openNotification('success', { message: 'Xóa thành công' })
          onRefresh()
          await refreshData('csytFavourite')
        } catch (error) {
          showError(error)
        } finally {
          setFetchLoading(false)
        }
      }
    }),
    [client?.homepageModule, refreshData]
  )
  const onSubmitSearch = async (value: string) => {
    console.log(11111, 'hello-1234234234')
  }

  const onRefresh = async () => {
    console.log(11111, 'hello-12')
  }

  const onPressViewDetail = async (id: string) => {
    console.log(11111, 'hello-1')
  }

  const onSubmitCreate = async (values: any, cancelModal: any) => {
    console.log(11111, 'hello-3333')
  }

  const onSubmitDelete = async (id: string) => {
    console.log(11111, 'hello-5666')
  }

  const onSubmitUpdate = async (values: any, cancelModal: any) => {
    console.log(11111, 'hello-77777')
  }

  const onChangePageEvent = (value: any) => {
    handleFilter({
      pageIndex: value - 1
    })
  }

  const onChangeSizeEvent = (value: any) => {
    handleFilter({
      pageSize: value,
      pageIndex: 0
    })
  }
  const onChangeTabEvent = async (value: any, level?: any) => {
    if (level === 'generalTab') {
      setTabActiveKey(value)
      switch (value) {
        case 'partner':
          await refreshData('partner')
          break
        case 'hospitals':
          await refreshData('csytFavourite')
          break
        default:
          break
      }
    }
    if (level === 'filterTab') {
      setSelectedFilterTab(value)
    }
  }

  const baseValueDoctorTelemed = useMemo(() => {
    return {
      doctorTelemedList: doctorTelemedList,
      allDoctorTelemedList: allDoctorTelemedList
    }
  }, [doctorTelemedList, allDoctorTelemedList])

  //#region Render
  return (
    <MPBrandPromotion
      onDragSortTable={onDragSortTable}
      heightContent={heightContent}
      hospitalListTab={hospitalLisTab}
      packageList={dataPartner} // Sử dụng dataPartner thay vì packageList
      loading={isLoading} // Sử dụng combined loading state
      onPressViewDetail={onPressViewDetail}
      // TTHT và đồng hành
      dataPartner={dataPartner}
      loadingDoctorTelemed={dataLoading.doctorTelemed} // Sử dụng loading từ custom hook
      baseValueDoctorTelemed={baseValueDoctorTelemed}
      methodsDoctorTelemed={methodsDoctorTelemed}
      actionCompanionship={actionCompanionship}
      // Cơ sở y tế yêu thích
      dataCSYTFavourite={dataCSYTFavourite}
      actionCSYTFavourite={actionCSYTFavourite}
      onSubmitSearch={onSubmitSearch}
      onSubmitCreate={onSubmitCreate}
      onSubmitUpdate={onSubmitUpdate}
      onSubmitDelete={onSubmitDelete}
      onRefresh={onRefresh}
      pagination={{
        current: filter.pageIndex > 0 ? filter.pageIndex + 1 : 1,
        pageSize: filter.pageSize,
        total:
          hospitalLisTab && hospitalLisTab?.length ? hospitalLisTab?.length : 0
      }}
      onChangePageEvent={onChangePageEvent}
      onChangeSizeEvent={onChangeSizeEvent}
      onChangeTabEvent={onChangeTabEvent}
      setTabActiveKey={setTabActiveKey}
      tabActiveKey={tabActiveKey}
    />
  )
  //#endregion
}
//#endregion
